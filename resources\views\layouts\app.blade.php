<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', isset($siteSettings) ? $siteSettings->site_name : 'SoloShop')</title>

    <!-- Favicon -->
    @if(isset($siteSettings) && $siteSettings->site_favicon)
        <link rel="icon" type="image/x-icon" href="{{ \App\Helpers\ImageHelper::getImageUrl($siteSettings->site_favicon) }}">
    @else
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    @endif
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    {{-- AdminLTE CSS เฉพาะหลังบ้าน --}}
    @if(request()->is('admin') || request()->is('admin/*'))
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css">
    @endif
    
    <style>
        body {
            font-family: 'Sarabun', sans-serif;
            background-color: #f8f9fa;
        }

        /* Memorial Theme Colors */
        :root {
            --primary-color: #34495e;
            --secondary-color: #2c3e50;
            --accent-color: #95a5a6;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --memorial-gold: #d4af37;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            min-height: 500px;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        /* Memorial Theme Enhancements */
        .hero-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.1) 100%);
            pointer-events: none;
        }
        
        .hero-section .container {
            position: relative;
            z-index: 1;
        }
        
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        
        .card-img-top {
            transition: transform 0.3s ease;
        }
        
        .card:hover .card-img-top {
            transform: scale(1.05);
        }
        
        .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 73, 94, 0.4);
        }
        
        .btn-outline-primary {
            border: 2px solid #34495e;
            color: #34495e;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            border-color: transparent;
            transform: translateY(-2px);
        }
        
        .section-title {
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 2rem;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            border-radius: 2px;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 3rem 0;
            margin-top: 4rem;
        }
        
        .social-links a {
            color: white;
            font-size: 1.5rem;
            margin: 0 10px;
            transition: color 0.3s ease;
        }
        
        .social-links a:hover {
            color: #667eea;
            transform: scale(1.2);
        }
        
        .navbar-nav .nav-link {
            position: relative;
            transition: color 0.3s ease;
        }
        
        .navbar-nav .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover::after {
            width: 100%;
        }
        
        .badge {
            border-radius: 20px;
            padding: 8px 16px;
        }
        
        .text-primary {
            color: #667eea !important;
        }
        
        .bg-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .package-description, .service-description, .article-content {
            line-height: 1.8;
            font-size: 1.1rem;
        }
        
        .sticky-top {
            z-index: 1020;
        }
        
        /* Memorial Floating Animation */
        @keyframes floating {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-15px);
            }
            100% {
                transform: translateY(0px);
            }
        }

        .floating-animation {
            animation: floating 6s ease-in-out infinite;
            opacity: 0.9;
        }

        /* Memorial Theme Card Styles */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(52, 73, 94, 0.1);
        }

        .card:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 15px 35px rgba(52, 73, 94, 0.15);
        }

        /* Memorial Theme Text Colors */
        .text-primary {
            color: var(--primary-color) !important;
        }

        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        /* Memorial Theme Feature Icons */
        .feature-icon i {
            color: var(--memorial-gold);
            text-shadow: 0 2px 4px rgba(212, 175, 55, 0.3);
        }

        /* Memorial Theme Navigation */
        .navbar-light .navbar-brand {
            color: var(--text-dark) !important;
            font-weight: 600;
        }

        .navbar-light .navbar-nav .nav-link {
            color: var(--text-dark) !important;
            font-weight: 500;
        }

        .navbar-light .navbar-nav .nav-link:hover {
            color: var(--memorial-gold) !important;
        }

        /* Memorial Theme Special Effects */
        .hero-section .display-4 {
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .hero-section .lead {
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* Memorial Theme Footer */
        .bg-dark {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%) !important;
        }

        /* Memorial Theme Buttons Enhancement */
        .btn-light {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            color: var(--text-dark);
            font-weight: 600;
        }

        .btn-light:hover {
            background: var(--memorial-gold);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }

        .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.8);
            color: rgba(255, 255, 255, 0.9);
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--memorial-gold);
            color: var(--memorial-gold);
        }

        /* Gallery Styles */
        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .gallery-image-container {
            position: relative;
            overflow: hidden;
            height: 300px;
        }

        .gallery-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .gallery-item:hover .gallery-image {
            transform: scale(1.1);
        }

        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(52, 73, 94, 0.8) 0%, rgba(44, 62, 80, 0.9) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .gallery-item:hover .gallery-overlay {
            opacity: 1;
        }

        .gallery-overlay-content {
            text-align: center;
            padding: 20px;
        }

        .gallery-overlay-content h5 {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .gallery-overlay-content i {
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .gallery-item:hover .gallery-overlay-content i {
            opacity: 1;
            transform: scale(1.1);
        }

        /* Gallery Modal Styles */
        .modal-content.bg-transparent {
            background: rgba(0,0,0,0.9) !important;
        }

        .btn-close-white {
            filter: invert(1) grayscale(100%) brightness(200%);
        }

        @media (max-width: 768px) {
            .hero-section {
                min-height: 400px;
                text-align: center;
            }

            .display-4 {
                font-size: 2.5rem;
            }

            .btn-lg {
                padding: 12px 24px;
                font-size: 1rem;
            }

            /* Gentle floating animation on mobile */
            .floating-animation {
                animation: floating 8s ease-in-out infinite;
            }

            .hero-section .display-4 {
                font-size: 2rem;
                text-shadow: 0 1px 3px rgba(0,0,0,0.3);
            }

            /* Gallery Mobile Styles */
            .gallery-image-container {
                height: 250px;
            }

            .gallery-overlay-content {
                padding: 15px;
            }

            .gallery-overlay-content h5 {
                font-size: 1rem;
            }

            .gallery-overlay-content p {
                font-size: 0.9rem;
            }

            .gallery-overlay-content i {
                font-size: 1.5rem !important;
            }
        }
    </style>
</head>
<body class="@if(request()->is('admin') || request()->is('admin/*')) hold-transition sidebar-mini @endif">
@if(request()->is('admin') || request()->is('admin/*'))
    <div class="wrapper">
        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <a href="/admin" class="brand-link text-center">
                @if(isset($siteSettings) && $siteSettings->site_logo)
                    <img src="{{ \App\Helpers\ImageHelper::getImageUrl($siteSettings->site_logo) }}"
                         alt="{{ $siteSettings->site_name ?? 'SoloShop' }}"
                         class="brand-image img-circle elevation-3"
                         style="max-height: 40px; width: auto;">
                @else
                    <i class="fas fa-cogs fa-2x"></i>
                @endif
                <span class="brand-text font-weight-light">
                    {{ isset($siteSettings) ? $siteSettings->site_name : 'SoloShop' }} Admin
                </span>
            </a>
            <div class="sidebar">
                <!-- User Panel -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-white"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block text-white">{{ Auth::user()->name }}</a>
                        <small class="text-light">Administrator</small>
                    </div>
                </div>

                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">

                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->is('admin') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>แดชบอร์ด</p>
                            </a>
                        </li>

                        <!-- จัดการเนื้อหาเว็บไซต์ -->
                        <li class="nav-header">จัดการเนื้อหาเว็บไซต์</li>

                        <li class="nav-item">
                            <a href="{{ route('admin.homepage.index') }}" class="nav-link {{ request()->is('admin/homepage*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-home"></i>
                                <p>หน้าแรก</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('admin.services.index') }}" class="nav-link {{ request()->is('admin/services*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-tools"></i>
                                <p>บริการ</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('admin.packages.index') }}" class="nav-link {{ request()->is('admin/packages*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-box"></i>
                                <p>แพ็กเกจ</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('admin.articles.index') }}" class="nav-link {{ request()->is('admin/articles*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-newspaper"></i>
                                <p>บทความ/ข่าวสาร</p>
                            </a>
                        </li>

                        <!-- จัดการข้อมูล -->
                        <li class="nav-header">จัดการข้อมูล</li>

                        <li class="nav-item">
                            <a href="{{ route('admin.contacts.index') }}" class="nav-link {{ request()->is('admin/contacts*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-envelope"></i>
                                <p>
                                    ข้อความติดต่อ
                                    @php
                                        $unreadCount = \App\Models\Contact::where('is_read', false)->count();
                                    @endphp
                                    @if($unreadCount > 0)
                                        <span class="badge badge-danger right">{{ $unreadCount }}</span>
                                    @endif
                                </p>
                            </a>
                        </li>

                        <!-- ระบบ -->
                        <li class="nav-header">ระบบ</li>

                        <li class="nav-item">
                            <a href="{{ route('admin.settings.index') }}" class="nav-link {{ request()->is('admin/settings*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-cog"></i>
                                <p>การตั้งค่าเว็บไซต์</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/" class="nav-link" target="_blank">
                                <i class="nav-icon fas fa-external-link-alt"></i>
                                <p>ดูเว็บไซต์</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <form method="POST" action="{{ route('logout') }}" class="nav-link" style="border: none; background: none; padding: 0;">
                                @csrf
                                <button class="btn btn-link nav-link text-danger" type="submit">
                                    <i class="nav-icon fas fa-sign-out-alt"></i>
                                    <p>ออกจากระบบ</p>
                                </button>
                            </form>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper" style="min-height: 100vh;">
            <section class="content pt-3">
                <div class="container-fluid">
                    @yield('content')
                </div>
            </section>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
@else
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="/">
                @if(isset($siteSettings) && $siteSettings->site_logo)
                    <img src="{{ \App\Helpers\ImageHelper::getImageUrl($siteSettings->site_logo) }}"
                         alt="{{ $siteSettings->site_name ?? 'SoloShop' }}"
                         style="height: 40px; width: auto;" class="me-2">
                @else
                    <i class="fas fa-shopping-cart me-2"></i>
                @endif
                {{ isset($siteSettings) ? $siteSettings->site_name : 'SoloShop' }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="/">
                            <i class="fas fa-home me-1"></i>หน้าแรก
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="/services">
                            <i class="fas fa-tools me-1"></i>บริการ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="/packages">
                            <i class="fas fa-box me-1"></i>แพ็กเกจ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="/articles">
                            <i class="fas fa-newspaper me-1"></i>บทความ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="/contact">
                            <i class="fas fa-phone me-1"></i>ติดต่อเรา
                        </a>
                    </li>
                    @auth
                        <li class="nav-item">
                            <a class="nav-link fw-medium text-success" href="/admin">
                                <i class="fas fa-cogs me-1"></i>หลังบ้าน
                            </a>
                        </li>
                        <li class="nav-item">
                            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                @csrf 
                                <button class="btn btn-link nav-link text-danger fw-medium" type="submit">
                                    <i class="fas fa-sign-out-alt me-1"></i>ออกจากระบบ
                                </button>
                            </form>
                        </li>
                    @else
                        <li class="nav-item">
                            <a class="nav-link fw-medium" href="/login">
                                <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบ
                            </a>
                        </li>
                    @endauth
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5 class="mb-3">SoloShop</h5>
                    <p>แพลตฟอร์มการขายออนไลน์ที่ครบครันสำหรับธุรกิจของคุณ</p>
                </div>
                <div class="col-md-4">
                    <h5 class="mb-3">เมนูหลัก</h5>
                    <ul class="list-unstyled">
                        <li><a href="/services" class="text-light text-decoration-none">บริการ</a></li>
                        <li><a href="/packages" class="text-light text-decoration-none">แพ็กเกจ</a></li>
                        <li><a href="/articles" class="text-light text-decoration-none">บทความ</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="mb-3">ติดต่อเรา</h5>
                    <p><i class="fas fa-phone me-2"></i>************</p>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-line"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 SoloShop. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
@endif
</body>
</html> 